// ProfileBio.tsx

import React, { useEffect, useState } from 'react';
import {
    Text,
    YStack,
    XStack,
    Avatar,
    Spacer,
    Button,
    Input,
} from 'tamagui';
import { ConnectionsDialog } from '@/components/ConnectionsDialog';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as ImagePicker from 'expo-image-picker';
import { uploadImage, updateUserProfile } from '@/api/usersAPI';
import { AxiosError } from 'axios';
import { Alert } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useToastController } from '@tamagui/toast';

interface ProfileBioProps {
    firstName: string;
    lastName: string;
    username: string;
    bio: string;
    profileImage: string;
    musicProvider: string;
}

const ProfileBio: React.FC<ProfileBioProps> = ({
                                                   firstName: initialFirstName,
                                                   lastName: initialLastName,
                                                   username: initialUsername,
                                                   bio: initialBio,
                                                   profileImage: initialProfileImage,
                                                   musicProvider: initialMusicProvider,
                                               }) => {
    const [isEditing, setIsEditing] = useState<boolean>(false);
    const [firstName, setFirstName] = useState<string>(initialFirstName);
    const [lastName, setLastName] = useState<string>(initialLastName);
    const [username, setUsername] = useState<string>(initialUsername);
    const [bio, setBio] = useState<string>(initialBio);
    const [profileImage, setProfileImage] = useState<string | null>(initialProfileImage);
    const [musicProvider, setMusicProvider] = useState<string>(initialMusicProvider);

    const queryClient = useQueryClient();
    const toast = useToastController();

    useEffect(() => {
        setFirstName(initialFirstName);
        setLastName(initialLastName);
        setUsername(initialUsername);
        setBio(initialBio);
        setProfileImage(initialProfileImage);
        setMusicProvider(initialMusicProvider);
    }, [
        initialFirstName,
        initialLastName,
        initialUsername,
        initialBio,
        initialProfileImage,
        initialMusicProvider,
    ]);

    const { mutateAsync: saveUserData } = useMutation({
        mutationFn: updateUserProfile,
        onSuccess: () => {
            toast.show('Profile updated successfully', {
                theme: 'green',
            });
            queryClient.invalidateQueries({ queryKey: ['userData'] });
            setIsEditing(false);
        },
        onError: (error) => {
            toast.show('Error updating profile', {
                theme: 'red',
            });
            console.log('Error updating profile:', error.message || error);
        },
    });

    const uploadImageMutation = useMutation({
        mutationFn: uploadImage,
        onSuccess: () => {
            // console.log("Image Upload Successful")
        },
        onError: (err: AxiosError) => {
            console.log(err);
            Alert.alert(
                'Error',
                'An error occurred while uploading the image. Please try again later.'
            );
        },
    });

    const pickImage = async () => {
        let result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [4, 3],
            quality: 1,
        });

        if (!result.canceled) {
            const uri = result.assets[0].uri;
            setProfileImage(uri);
            uploadImageMutation.mutate({ uri });
        }
    };

    const handleSaveChanges = async () => {
        await saveUserData({
            firstName,
            lastName,
            username,
            bio,
            musicProvider,
        });
    };

    return (
        <YStack
            padding={20}
            backgroundColor="#141414"
            borderRadius={16}
            shadowColor="#000"
            shadowOpacity={0.3}
            shadowRadius={12}
            shadowOffset={{ width: 0, height: 6 }}
            marginHorizontal={20}
            width="90%"
        >
            <XStack alignItems="center" marginBottom={15} position="relative">
                <YStack position="relative">
                    <Avatar circular size="$10">
                        {profileImage ? (
                            <Avatar.Image
                                accessibilityLabel={'Profile Picture'}
                                src={profileImage}
                            />
                        ) : (
                            <Avatar.Fallback backgroundColor="#fed900" />
                        )}
                    </Avatar>
                    {isEditing && (
                        <Button
                            size="$2"
                            position="absolute"
                            bottom={0}
                            right={0}
                            onPress={pickImage}
                            icon={<MaterialIcons name="edit" size={16} color="white" />}
                        />
                    )}
                </YStack>

                <YStack marginLeft={15} flex={1}>
                    {isEditing ? (
                        <>
                            <Input
                                value={firstName}
                                onChangeText={setFirstName}
                                placeholder="First Name"
                                marginBottom="$2"
                            />
                            <Input
                                value={lastName}
                                onChangeText={setLastName}
                                placeholder="Last Name"
                                marginBottom="$2"
                            />
                            <Input
                                value={username}
                                onChangeText={(text) => setUsername(text.replace(/\s/g, ''))}
                                placeholder="Username"
                                marginBottom="$2"
                            />
                        </>
                    ) : (
                        <>
                            <Text fontSize={22} fontWeight="800" color="#fff">
                                {firstName} {lastName}
                            </Text>
                            <Text fontSize={14} color="#aaa" fontStyle="italic">
                                @{username}
                            </Text>
                        </>
                    )}
                </YStack>
                {!isEditing && (
                    <Button
                        size="$2"
                        position="absolute"
                        top={10}
                        right={10}
                        onPress={() => setIsEditing(true)}
                        icon={<MaterialIcons name="edit" size={16} color="white" />}
                    />
                )}
            </XStack>
            {isEditing ? (
                <>
                    <Input
                        value={bio}
                        onChangeText={setBio}
                        placeholder="Bio"
                        multiline
                        numberOfLines={4}
                        textAlignVertical="top"
                        marginBottom="$2"
                    />
                    <XStack space="$2">
                        <Button flex={1} onPress={handleSaveChanges}>
                            Save Changes
                        </Button>
                        <Button
                            flex={1}
                            onPress={() => {
                                setIsEditing(false);
                                setFirstName(initialFirstName);
                                setLastName(initialLastName);
                                setUsername(initialUsername);
                                setBio(initialBio);
                                setProfileImage(initialProfileImage);
                            }}
                        >
                            Cancel
                        </Button>
                    </XStack>
                </>
            ) : bio ? (
                <Text
                    fontSize={18}
                    fontStyle="italic"
                    color="#ddd"
                    lineHeight={24}
                    textAlign="left"
                >
                    “{bio.trim()}”
                </Text>
            ) : (
                <Text
                    fontSize={18}
                    fontStyle="italic"
                    color="#ddd"
                    lineHeight={24}
                    textAlign="left"
                >
                    No bio
                </Text>
            )}

            <Spacer size="$5" />
            <ConnectionsDialog />
        </YStack>
    );
};

export default ProfileBio;
