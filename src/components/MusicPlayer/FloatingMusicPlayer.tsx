import React, {useState} from 'react'
import {Image} from 'react-native'
import {Button, Text, YStack} from 'tamagui'
import {Pause, Play} from '@tamagui/lucide-icons'

type CompactMusicPlayerProps = {
    albumCover: string
    songTitle: string
    artistName: string
}

export function FloatingMusicPlayer({
                                        albumCover,
                                        songTitle,
                                        artistName,
                                    }: CompactMusicPlayerProps) {
    const [isPlaying, setIsPlaying] = useState(false)
    const [isExpanded, setIsExpanded] = useState(true)

    const togglePlay = (event?: any) => {
        // Prevent the collapse/expand when clicking play button
        event?.stopPropagation()
        setIsPlaying((prev) => !prev)
    }

    const toggleExpand = () => setIsExpanded((prev) => !prev)

    return (
        <YStack
            position="absolute"
            bottom="$10"
            right="$1"
            zIndex={999999}
            backgroundColor="$background"
            borderRadius="$4"
            elevation={8}
            style={{
                shadowColor: '#000',
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.3,
                shadowRadius: 4,
            }}
            width={isExpanded ? 380 : 64}
            height={75}
            // Add press handler to the container
            pressStyle={{opacity: 0.8}}
            onPress={toggleExpand}
        >
            <YStack
                flexDirection="row"
                alignItems="center"
                padding="$2"
                flex={1}
            >
                <Image
                    source={{uri: albumCover}}
                    style={{
                        width: 70,
                        height: 70,
                        borderRadius: 8,
                    }}
                    resizeMode="cover"
                />

                {/* Song info, only if expanded */}
                {isExpanded && (
                    <YStack marginLeft="$3" flex={1} overflow="hidden">
                        <Text fontWeight="bold" numberOfLines={1}>
                            {songTitle}
                        </Text>
                        <Text opacity={0.7} numberOfLines={1}>
                            {artistName}
                        </Text>
                    </YStack>
                )}

                {/* Play / Pause button, only if expanded */}
                {isExpanded && (
                    <Button
                        size="$4"
                        icon={isPlaying ? Pause : Play}
                        onPress={togglePlay}
                        theme="alt1"
                        marginRight="$3"
                        // Prevent the button press from triggering container's onPress
                        hitSlop={8}
                    />
                )}
            </YStack>
        </YStack>
    )
}