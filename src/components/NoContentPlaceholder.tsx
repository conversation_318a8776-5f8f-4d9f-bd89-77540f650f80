import React from 'react';
import { View } from 'react-native';
import { Text, YStack } from 'tamagui';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';

const NoContentPlaceholder: React.FC = () => {
    return (
        <YStack
            flex={1}
            justifyContent="center"
            alignItems="center"
            backgroundColor="#000000"
            borderWidth={1}
            borderColor="#ffd900"
            borderRadius={25}
            padding={25}
            margin={25}
        >
            <MaterialCommunityIcons name="party-popper"  size={50} color="#ffd900"/>
            <Text fontSize={24} fontWeight="bold" color="#ffd900" marginTop={20}>
                Help kick off the party!
            </Text>
            <Text fontSize={16} color="#ffd900" textAlign="center" marginTop={8}>
                Invite more friends to get the music flowing and the party started!
            </Text>
        </YStack>
    );
};

export default NoContentPlaceholder;
