import React, {useEffect, useState} from 'react';
import {styled, Text, YStack} from 'tamagui';
import {Marquee} from '@animatereactnative/marquee';

type ConditionalMarqueeTextProps = {
    text: string;
    fontSize: number;
    bold?: boolean;
    color: string;
    width: string;
};

const StyledText = styled(Text, {
    variants: {
        bold: {
            true: {fontWeight: 'bold'},
            false: {fontWeight: 'normal'},
        },
    } as const,
});

const ConditionalMarqueeText: React.FC<ConditionalMarqueeTextProps> = ({text, fontSize, bold, color, width}) => {
    const [isOverflowing, setIsOverflowing] = useState(false);
    const [containerWidth, setContainerWidth] = useState(0);

    useEffect(() => {
        setIsOverflowing(calculateTextWidth(text, fontSize, bold) > containerWidth);
    }, [text, fontSize, bold, containerWidth]);

    const calculateTextWidth = (text: string, fontSize: number, isBold: boolean | undefined) => {
        const averageCharWidth = isBold ? fontSize * 0.65 : fontSize * 0.6;
        return text.length * averageCharWidth;
    };

    return (
        <YStack
            width={width}
            overflow="hidden"
            onLayout={(event) => setContainerWidth(event.nativeEvent.layout.width)}
        >
            {isOverflowing ? (
                <Marquee spacing={20} speed={0.25}>
                    <StyledText bold={bold} color={color} fontSize={fontSize}>
                        {text}
                    </StyledText>
                </Marquee>
            ) : (
                <StyledText bold={bold} color={color} fontSize={fontSize} numberOfLines={1}>
                    {text}
                </StyledText>
            )}
        </YStack>
    );
};

export default ConditionalMarqueeText;