import React, {useEffect, useState} from 'react';
import {TouchableOpacity} from 'react-native';
import {<PERSON>ton, Card, Image, Spinner, Text, XStack, YStack, ZStack,} from 'tamagui';
import {LinearGradient} from 'expo-linear-gradient';
import {<PERSON>ather, FontAwesome6} from '@expo/vector-icons';
import {Link} from 'expo-router';
import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import ConditionalMarqueeText from '@/components/Feed/FeedCard/ConditionalMarqueeText';
import {useMusicPlayerStore} from '@/stores/useMusicPlayerStore';
import {getUserData} from '@/api/usersAPI';
import {updatePostRating} from '@/api/postsAPI';
import CommentSection from '@/components/Feed/FeedCard/CommentSection';
import {Audio} from 'expo-av';
import MusicProviderLink from "@/components/MusicProviderLink";

type UpdatePostRatingParams = {
    postID: string;
    reaction_type: 'Like' | 'Dislike' | null;
};

type UserReaction = 'Like' | 'Dislike' | null;

type Comment = {
    id: string;
    username: string;
    text: string;
    commenterProfilePicture?: string;
    createdAt: string;
};

type FeedCardProps = {
    postID: string;
    posterUsername: string;
    posterProfilePicture: string;
    artworkURL: string;
    artistName: string;
    trackName: string;
    postRating: number;
    audioPreviewURL: string;
    spotifyURL?: string;
    trackURL?: string;
    userReaction: UserReaction;
    comments: Comment[];
    audioCommentURL?: string | null;
    hideActionButtons?: boolean;
};

const FeedCard: React.FC<FeedCardProps> = ({
                                               postID,
                                               posterUsername,
                                               posterProfilePicture,
                                               artworkURL,
                                               artistName,
                                               trackName,
                                               postRating,
                                               audioPreviewURL,
                                               spotifyURL,
                                               trackURL,
                                               userReaction,
                                               comments,
                                               audioCommentURL,
                                               hideActionButtons = false,
                                           }) => {
    const [showComments, setShowComments] = useState(false);
    const queryClient = useQueryClient();
    const {playingUrl, isPlaying, loadAndPlayTrack, togglePlayPause} = useMusicPlayerStore();
    const [sound, setSound] = useState<Audio.Sound | null>(null);

    const userData = useQuery({
        queryKey: ['userData'],
        queryFn: getUserData,
    });

    const {mutateAsync} = useMutation({
        mutationFn: (params: UpdatePostRatingParams) =>
            updatePostRating(params.postID, params.reaction_type),
        onSuccess: () => {
            queryClient.invalidateQueries({queryKey: ['userFeed']});
        },
    });

    const handlePress = async (postID: string, reaction_type: 'Like' | 'Dislike') => {
        let newReactionType: 'Like' | 'Dislike' | null = reaction_type;

        if (userReaction === reaction_type) {
            newReactionType = null;
        }

        await mutateAsync({postID, reaction_type: newReactionType});
    };

    const isCurrentTrackPlaying = playingUrl === audioPreviewURL && isPlaying;

    const playAudio = async () => {
        if (audioCommentURL) {
            // console.log('Playing audio comment');
            const {sound: commentSound} = await Audio.Sound.createAsync({uri: audioCommentURL});
            setSound(commentSound);
            await commentSound.playAsync();
            commentSound.setOnPlaybackStatusUpdate(async (status) => {
                if (status.didJustFinish) {
                    const {sound: songSound} = await Audio.Sound.createAsync({uri: audioPreviewURL});
                    setSound(songSound);
                    await songSound.playAsync();
                }
            });
        } else {
            const {sound: songSound} = await Audio.Sound.createAsync({uri: audioPreviewURL});
            setSound(songSound);
            await songSound.playAsync();
        }
    };

    useEffect(() => {
        return sound
            ? () => {
                sound.unloadAsync();
            }
            : undefined;
    }, [sound]);

    return (
        <Card width="100%" height={420} borderRadius={10} overflow="hidden">
            <YStack
                position="absolute"
                zIndex={3}
                width="22.5%"
                height="auto"
                top={0}
                left={0}
                backgroundColor="rgba(0, 0, 0, 0.9)"
                padding="$2"
                borderBottomRightRadius={10}
            >
                <Text
                    color="white"
                    numberOfLines={1}
                    adjustsFontSizeToFit
                    minimumFontScale={0.5}
                    textShadowColor="rgba(255, 255, 255, 0.8)"
                    textShadowOffset={{width: 0, height: 0}}
                    textShadowRadius={10}
                >
                    @{posterUsername}
                </Text>
            </YStack>
            <Card.Background>
                <Image
                    source={{uri: posterProfilePicture}}
                    style={{width: '100%', height: '100%'}}
                    zIndex={1}
                />
            </Card.Background>
            <ZStack alignItems="center" justifyContent="center" width="100%" height="100%">
                <YStack
                    position="absolute"
                    zIndex={2}
                    width="80%"
                    height="60%"
                    top="40%"
                    left="12.5%"
                >
                    <LinearGradient
                        colors={['rgba(26, 26, 26, 0.8)', 'rgba(42, 42, 42, 0.8)']}
                        start={{x: 0, y: 0}}
                        end={{x: 1, y: 1}}
                        style={{
                            borderRadius: 20,
                            padding: 2,
                            shadowColor: '#000',
                            shadowOffset: {width: 0, height: 5},
                            shadowOpacity: 0.3,
                            shadowRadius: 10,
                            elevation: 5,
                        }}
                    >
                        <XStack
                            backgroundColor="rgba(0, 0, 0, 0.5)"
                            borderRadius={18}
                            overflow="hidden"
                            alignItems="center"
                            padding={10}
                            space="$3"
                        >
                            <Image source={{uri: artworkURL}} style={{width: 70, height: 70}}/>
                            <YStack flex={1} space="$2">
                                <ConditionalMarqueeText
                                    text={trackName}
                                    fontSize={16}
                                    bold
                                    color="white"
                                    width="99%"
                                />
                                <ConditionalMarqueeText
                                    text={artistName}
                                    fontSize={12}
                                    color="rgba(255, 255, 255, 0.7)"
                                    width="99%"
                                />
                                <XStack alignItems="center" space="$3">
                                    <TouchableOpacity onPress={playAudio}>
                                        <Feather
                                            name={isCurrentTrackPlaying ? 'pause' : 'play'}
                                            size={24}
                                            color="white"
                                        />
                                    </TouchableOpacity>
                                    <Text fontSize={14} color="rgba(255, 255, 255, 0.7)">
                                        {postRating} 🔥
                                    </Text>
                                </XStack>
                            </YStack>
                            {!hideActionButtons && (
                                <YStack
                                    alignItems="center"
                                    justifyContent="space-between"
                                    height={70}
                                    space="$3"
                                >
                                    <TouchableOpacity onPress={() => handlePress(postID, 'Like')}>
                                        <Feather
                                            name="thumbs-up"
                                            size={22}
                                            color={userReaction === 'Like' ? '#4CAF50' : 'white'}
                                        />
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => handlePress(postID, 'Dislike')}>
                                        <Feather
                                            name="thumbs-down"
                                            size={22}
                                            color={userReaction === 'Dislike' ? '#F44336' : 'white'}
                                        />
                                    </TouchableOpacity>
                                </YStack>
                            )}
                        </XStack>
                    </LinearGradient>
                </YStack>

                <YStack
                    position="absolute"
                    zIndex={4}
                    bottom={10}
                    left={10}
                    backgroundColor="rgba(0, 0, 0, 0.7)"
                    padding="$2"
                    borderRadius={20}
                >
                    <TouchableOpacity onPress={() => setShowComments(true)}>
                        <XStack space="$2" alignItems="center">
                            <Feather name="message-circle" size={20} color="white"/>
                            <Text color="white">{comments.length}</Text>
                        </XStack>
                    </TouchableOpacity>
                </YStack>
            </ZStack>

            <CommentSection
                postID={postID}
                comments={comments}
                hideActionButtons={hideActionButtons}
                showComments={showComments}
                setShowComments={setShowComments}
            />

            {userData.isLoading ? (
                <Spinner size="large" color="#fed900"/>
            ) : (
                <>
                    <YStack
                        alignSelf="flex-end"
                        position="absolute"
                        right={10}
                        bottom={10}
                        zIndex={99}

                    >
                        <MusicProviderLink
                            trackURL={trackURL}
                            size={24}
                        />
                    </YStack>
                </>
            )}
        </Card>
    );
};

export default FeedCard;