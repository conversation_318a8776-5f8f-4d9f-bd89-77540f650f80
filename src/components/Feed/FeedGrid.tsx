import React, {useMemo, useRef} from 'react';
import {Dimensions, Pressable, Text, View} from 'react-native';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';
import Animated, {
    useAnimatedProps,
    useAnimatedStyle,
    useDerivedValue,
    useSharedValue,
    withTiming,
} from 'react-native-reanimated';
import FeedCard from '@/components/Feed/FeedCard/FeedCard'; // Adjust the import path as needed

// Screen dimensions
const {width, height} = Dimensions.get('window');

// Configurable variables for card size and spacing
const _itemWidthPercentage = 0.80; // Card width as percentage of screen width (0 to 1)
const _itemHeight = 500; // Card height in pixels
const _horizontalSpacing = 25; // Horizontal space between cards in pixels
const _verticalSpacing = 50; // Vertical space between cards in pixels

// Derived constants
const _itemWidth = width * _itemWidthPercentage;
const _gridLength = 5;

// Calculate the total grid width and height
const totalGridWidth = _gridLength * _itemWidth + (_gridLength - 1) * _horizontalSpacing;
const totalGridHeight = _gridLength * _itemHeight + (_gridLength - 1) * _verticalSpacing;

// Spiral order for inner positions (rows 1-3, cols 1-3) starting from center
const innerOrder = [
    {row: 2, col: 2}, // Center
    {row: 2, col: 3},
    {row: 3, col: 3},
    {row: 3, col: 2},
    {row: 3, col: 1},
    {row: 2, col: 1},
    {row: 1, col: 1},
    {row: 1, col: 2},
    {row: 1, col: 3},
];

// DummyCard for unfilled inner positions
const DummyCard = () => (
    <View
        style={{
            width: '100%',
            height: _itemHeight,
            backgroundColor: '#333',
            justifyContent: 'center',
            alignItems: 'center',
        }}
    >
        <Text style={{color: 'white'}}>Dummy Card</Text>
    </View>
);

// AdCard for edge positions
const AdCard = () => (
    <View
        style={{
            width: '100%',
            height: _itemHeight,
            backgroundColor: '#444',
            justifyContent: 'center',
            alignItems: 'center',
        }}
    >
        <Text style={{color: 'white'}}>Ad Card</Text>
    </View>
);

// GridItem component with zoom effect and click navigation
const GridItem = ({row, col, cardComponent, activeIndex, index, onPress}) => {
    const stylez = useAnimatedStyle(() => {
        return {
            opacity: activeIndex.value === index ? withTiming(1, {duration: 300}) : withTiming(0.4),
            transform: [
                {
                    scale: activeIndex.value === index ? withTiming(1.1, {duration: 300}) : withTiming(1),
                },
            ],
        };
    });

    return (
        <Pressable onPress={() => onPress(row, col)}>
            <Animated.View
                style={[
                    {
                        width: _itemWidth,
                        height: _itemHeight,
                        marginHorizontal: _horizontalSpacing / 2,
                        marginVertical: _verticalSpacing / 2,
                    },
                    stylez,
                ]}
            >
                {cardComponent}
            </Animated.View>
        </Pressable>
    );
};

export default function FeedGrid({feedCards = [], userPost}) {
    const ref = useRef();
    const x = useSharedValue(2); // Start at col 2 (third column)
    const y = useSharedValue(2); // Start at row 2 (third row)
    const canSwipe = useSharedValue(true);

    // Pan gesture for swipe navigation
    const panGesture = Gesture.Pan()
        .minDistance(20)
        .onBegin(() => {
            canSwipe.value = true;
        })
        .onEnd((event) => {
            if (canSwipe.value) {
                canSwipe.value = false;
                if (Math.abs(event.translationX) > 20) {
                    if (event.velocityX < -20) {
                        x.value = clamp(x.value + 1, 0, _gridLength - 1);
                    } else if (event.velocityX > 20) {
                        x.value = clamp(x.value - 1, 0, _gridLength - 1);
                    }
                }
                if (Math.abs(event.translationY) > 20) {
                    if (event.velocityY < -20) {
                        y.value = clamp(y.value + 1, 0, _gridLength - 1);
                    } else if (event.velocityY > 20) {
                        y.value = clamp(y.value - 1, 0, _gridLength - 1);
                    }
                }
            }
        });

    // Calculate the active card's index
    const activeIndex = useDerivedValue(() => {
        return y.value * _gridLength + x.value;
    });

    // Animate ScrollView to center the active card with clamping
    const animatedProps = useAnimatedProps(() => {
        const cardCenterX = x.value * (_itemWidth + _horizontalSpacing) + _itemWidth / 2;
        const cardCenterY = y.value * (_itemHeight + _verticalSpacing) + _itemHeight / 2;

        // Calculate target offsets to center the card
        let targetX = cardCenterX - width / 2;
        let targetY = cardCenterY - height / 2;

        // Define min and max offsets to keep adjacent cards visible
        const minOffsetX = 0;
        const maxOffsetX = Math.max(0, totalGridWidth - width);
        const minOffsetY = 0;
        const maxOffsetY = Math.max(0, totalGridHeight - height);

        // Clamp the target offsets
        targetX = clamp(targetX, minOffsetX, maxOffsetX);
        targetY = clamp(targetY, minOffsetY, maxOffsetY);

        return {
            contentOffset: {
                x: withTiming(targetX, {duration: 300}, () => {
                    canSwipe.value = true;
                }),
                y: withTiming(targetY, {duration: 300}, () => {
                    canSwipe.value = true;
                }),
            },
        };
    });

    // Generate the 5x5 grid
    const grid = useMemo(() => {
        const gridArray = [];
        for (let row = 0; row < _gridLength; row++) {
            const rowArray = [];
            for (let col = 0; col < _gridLength; col++) {
                let cardComponent;
                if (row === 0 && col === 1) {
                    // User's post at second card in first row
                    cardComponent = userPost ? (
                        <FeedCard {...userPost} height={_itemHeight} hideActionButtons={true}/>
                    ) : (
                        <DummyCard/>
                    );
                } else if (
                    row === 0 ||
                    row === _gridLength - 1 ||
                    col === 0 ||
                    col === _gridLength - 1
                ) {
                    // Edge positions for ads
                    cardComponent = <AdCard/>;
                } else {
                    // Inner positions for feed cards
                    const positionIndex = innerOrder.findIndex(
                        (p) => p.row === row && p.col === col
                    );
                    if (positionIndex < feedCards.length) {
                        cardComponent = <FeedCard {...feedCards[positionIndex]} height={_itemHeight}/>;
                    } else {
                        cardComponent = <DummyCard/>;
                    }
                }
                rowArray.push(cardComponent);
            }
            gridArray.push(rowArray);
        }
        return gridArray;
    }, [feedCards, userPost]);

    return (
        <GestureDetector gesture={panGesture}>
            <Animated.View style={{flex: 1, backgroundColor: '#0000'}}>
                <Animated.ScrollView
                    ref={ref}
                    scrollEnabled={false}
                    animatedProps={animatedProps}
                    contentContainerStyle={{
                        width: totalGridWidth,
                    }}
                    renderToHardwareTextureAndroid={true}
                >
                    {grid.map((row, rowIndex) => (
                        <View key={`row-${rowIndex}`} style={{flexDirection: 'row'}}>
                            {row.map((cardComponent, colIndex) => {
                                const index = rowIndex * _gridLength + colIndex;
                                return (
                                    <GridItem
                                        key={`item-${rowIndex}-${colIndex}`}
                                        row={rowIndex}
                                        col={colIndex}
                                        cardComponent={cardComponent}
                                        activeIndex={activeIndex}
                                        index={index}
                                        onPress={() => {
                                            x.value = colIndex;
                                            y.value = rowIndex;
                                        }}
                                    />
                                );
                            })}
                        </View>
                    ))}
                </Animated.ScrollView>
            </Animated.View>
        </GestureDetector>
    );
}

// Utility function to clamp values
function clamp(value, min, max) {
    'worklet';
    return Math.min(Math.max(value, min), max);
}