import React from "react";
import {Image, View} from "react-native";
import Animated, {
    FadeInRight,
    interpolate,
    interpolateColor,
    runOnJS,
    SharedValue,
    useAnimatedStyle,
    useDerivedValue,
    useSharedValue,
    withDelay,
    withSpring,
} from "react-native-reanimated";

// Constants
const _avatarSize = 28;
const _spacing = 4;
const _staggerDuration = 50;
const _initialDelayDuration = 1500;
// The height of the container where the bars are going to be rendered.
const _containerSize = 150;

// Re-maps a number from one range to another.
function mapRange(
    value: number,
    low1: number,
    high1: number,
    low2: number,
    high2: number
) {
    return low2 + ((high2 - low2) * (value - low1)) / (high1 - low1);
}

type User = {
    name: string;
    score: number;
    avatar: string; // Added avatar property
};

type PlaceProps = {
    user: User;
    index: number;
    onFinish?: () => void;
    minMax: number[];
    anim: SharedValue<number>;
};

function Place({user, onFinish, index, minMax, anim}: PlaceProps) {
    const _height = mapRange(
        user.score,
        minMax[0],
        minMax[1],
        _spacing * 4,
        _containerSize - _avatarSize
    );

    const _anim = useDerivedValue(() => {
        return withDelay(
            _staggerDuration * index,
            withSpring(anim.value, {damping: 80, stiffness: 200})
        );
    });

    const stylez = useAnimatedStyle(() => {
        return {
            height: _anim.value * _height + _avatarSize + _spacing,
            borderBottomLeftRadius: interpolate(
                _anim.value,
                [0, 1],
                [_avatarSize / 2, 0]
            ),
            borderBottomRightRadius: interpolate(
                _anim.value,
                [0, 1],
                [_avatarSize / 2, 0]
            ),
            backgroundColor:
                minMax[1] === user.score
                    ? interpolateColor(
                        _anim.value,
                        [0, 1],
                        ["rgba(255,255,255,0.1)", "#FFD700"] // Gold color
                    )
                    : "rgba(255,255,255,0.1)",
        };
    });

    // Only used for the text to be displayed after the animation has started.
    const textStylez = useAnimatedStyle(() => {
        return {
            opacity: interpolate(_anim.value, [0, 0.2, 1], [0, 0, 1]),
        };
    });

    return (
        <Animated.View
            entering={FadeInRight.delay(
                _staggerDuration * index + _initialDelayDuration
            )
                .springify()
                .damping(80)
                .stiffness(200)
                .withCallback((finished) => {
                    if (finished && onFinish) {
                        runOnJS(onFinish)();
                    }
                })}
            style={{alignItems: "center"}}
        >
            <Animated.View
                style={[
                    {
                        backgroundColor: "rgba(255,255,255,0.2)",
                        padding: _spacing / 2,
                        borderRadius: _avatarSize / 2 + _spacing,
                        gap: _spacing / 2,
                        alignItems: "center",
                    },
                    stylez,
                ]}
            >
                <View
                    style={{
                        width: _avatarSize,
                        aspectRatio: 1,
                        borderRadius: _avatarSize / 2,
                        borderWidth: 1,
                        borderColor: "#FFF",
                        borderStyle: "dashed",
                        padding: _spacing / 4,
                    }}
                >
                    <Image
                        source={{uri: user.avatar}}
                        style={{flex: 1, borderRadius: _avatarSize}}
                    />
                </View>
                <Animated.Text
                    style={[
                        {
                            fontSize: _avatarSize / 3,
                            fontWeight: "700",
                            fontFamily: "Inter",
                            color: "#FFF", // Set text color to white
                        },
                        textStylez,
                    ]}
                >
                    {user.score}
                </Animated.Text>
            </Animated.View>
        </Animated.View>
    );
}

type LeaderBoardProps = {
    users: User[];
};

export default function LeaderBoard({users}: LeaderBoardProps) {
    // Find the min and max score of the users
    const minMaxScoreOfUsers = users.reduce(
        (acc, user) => {
            if (user.score < acc[0]) {
                acc[0] = user.score;
            }
            if (user.score > acc[1]) {
                acc[1] = user.score;
            }
            return acc;
        },
        [Infinity, -Infinity]
    );

    const _anim = useSharedValue(0);

    return (
        <View
            style={{
                justifyContent: "center",
                alignItems: "center",
            }}
        >
            <View
                style={{
                    flexDirection: "row",
                    gap: _spacing,
                    justifyContent: "flex-end",
                    alignItems: "flex-end",
                    height: _containerSize,
                }}
            >
                {users.map((user, index) => (
                    <Place
                        key={index}
                        user={user}
                        index={index}
                        minMax={minMaxScoreOfUsers}
                        anim={_anim}
                        onFinish={
                            index === users.length - 1
                                ? () => {
                                    _anim.value = 1;
                                    console.log("Finished", index);
                                }
                                : undefined
                        }
                    />
                ))}
            </View>
        </View>
    );
}
