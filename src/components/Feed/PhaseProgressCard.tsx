import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Sep<PERSON>tor, Sheet, <PERSON>ack, Text, XStack, YStack} from 'tamagui';
import {<PERSON><PERSON><PERSON><PERSON>, Feather} from '@expo/vector-icons';
import {Share} from 'react-native';
import {Link} from 'expo-router';
import {useUserData} from "@/hooks/useUser";

interface Phase {
    name: string;
    startHour: number;
    endHour: number;
    icon: string;
    description: string;
}

const phases: Phase[] = [
    {
        name: 'Post Time',
        startHour: 18,
        endHour: 20,
        icon: '📝',
        description: 'During Post Time (18:00-20:00), users can share and submit their content. This is the main window for creating and publishing your posts.'
    },
    {
        name: 'Rating Time',
        startHour: 20,
        endHour: 24,
        icon: '⭐',
        description: 'Rating Time (20:00-24:00) is when the community evaluates and rates posted content. Your engagement during this period helps surface the best content.'
    },
    {
        name: 'Thinking Phase',
        startHour: 0,
        endHour: 18,
        icon: '💭',
        description: 'The Thinking Phase (00:00-18:00) is dedicated to content creation and ideation. Use this time to prepare your best content for the upcoming Post Time.'
    }
];

const formatHour = (hour: number): string => {
    return hour.toString().padStart(2, '0') + ':00';
};

const formatTimeInterval = (startHour: number, endHour: number): string => {
    return `${formatHour(startHour)} - ${formatHour(endHour)}`;
};


interface LevelData {
    level: number;
    levelName: string;
    friendsNeeded: number;
}


const PhaseProgressCard = () => {
    const [activePhase, setActivePhase] = useState<Phase | null>(null);
    const [timeRemaining, setTimeRemaining] = useState<string>('');
    const [isOpen, setIsOpen] = useState(false);
    const [infoOpen, setInfoOpen] = useState(false);

    const {
        data: userData,
        isLoading: isUserDataLoading,
        isError: isUserDataError,
        isSuccess: isUserDataSuccess
    } = useUserData();

    const getCurrentPhase = (date: Date): Phase | null => {
        const hour = date.getHours();
        return phases.find(phase => {
            if (phase.startHour < phase.endHour) {
                return hour >= phase.startHour && hour < phase.endHour;
            } else {
                return hour >= phase.startHour || hour < phase.endHour;
            }
        }) || null;
    };

    const calculateTimeRemaining = (date: Date, phase: Phase | null): string => {
        if (!phase) return '00:00:00';

        const now = date;
        const endTime = new Date(now);
        endTime.setHours(phase.endHour, 0, 0, 0);

        if (endTime < now) {
            endTime.setDate(endTime.getDate() + 1);
        }

        const diff = endTime.getTime() - now.getTime();
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    };

    useEffect(() => {
        const timer = setInterval(() => {
            const now = new Date();
            const phase = getCurrentPhase(now);
            setActivePhase(phase);
            setTimeRemaining(calculateTimeRemaining(now, phase));
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    const onShare = async () => {
        try {
            await Share.share({
                message: 'Check out this link: https://wullup.com',
                url: 'https://wullup.com',
                title: 'Share this link',
            });
        } catch (error) {
            console.error('Error sharing link:', error);
        }
    };

    if (isUserDataLoading) return <Text>Loading...</Text>;
    if (isUserDataError) return <Text>An error occurred: {isUserDataError.message}</Text>;

    const {level, levelName, friendsNeeded} = userData.levelInfo;

    return (
        <>
            <Card
                width="98%"
                overflow="hidden"
                backgroundColor="#121212"
                borderRadius={12}
            >
                <YStack padding="$4">
                    <XStack justifyContent="space-between" alignItems="center">
                        {/* Left Column: Level Info */}
                        <XStack space="$4" alignItems="center">
                            <YStack alignItems="center" justifyContent="center"
                                    backgroundColor="#2a2a2a"
                                    width={45}
                                    height={45}
                                    borderRadius={25}
                                    borderColor="#fed900"
                                    borderWidth={2}
                            >
                                <Text color="#fed900" fontWeight="700" fontSize={16}>
                                    {level}
                                </Text>
                            </YStack>

                            <YStack>
                                <Text color="white" fontSize={15} fontWeight="500">
                                    {levelName}
                                </Text>
                                <Text color="#fed900" fontSize={13}>
                                    +{friendsNeeded} friends to next level
                                </Text>
                            </YStack>
                        </XStack>

                        {/* Right Column: Action buttons */}
                        <XStack space="$2">
                            <Button
                                unstyled
                                circular
                                onPress={() => setInfoOpen(true)}
                                icon={<Feather name="info" size={20} color="#fed900"/>}
                            />
                            <Button
                                unstyled
                                circular
                                onPress={() => setIsOpen(!isOpen)}
                                icon={<Feather
                                    name={isOpen ? "chevron-up" : "chevron-down"}
                                    size={20}
                                    color="#fed900"
                                />}
                            />
                        </XStack>
                    </XStack>

                    <Separator marginVertical="$3" backgroundColor="#2a2a2a"/>

                    {/* Phase Timer Row */}
                    <XStack alignItems="center" space="$3">
                        <Feather name="clock" size={20} color="#fed900"/>
                        <YStack>
                            <Text color="#fed900" fontSize={22} fontWeight="600" fontFamily="$mono">
                                {timeRemaining}
                            </Text>
                            <Text color="white" opacity={0.7} fontSize={13}>
                                {activePhase?.icon} {activePhase?.name} {activePhase && `(${formatTimeInterval(activePhase.startHour, activePhase.endHour)})`}
                            </Text>
                        </YStack>
                    </XStack>

                    {isOpen && (
                        <YStack space="$4" marginTop="$4">
                            {/* Phases List */}
                            <Stack space="$2">
                                {phases.map((phase) => (
                                    <XStack
                                        key={phase.name}
                                        backgroundColor={activePhase?.name === phase.name ? '#2a2a2a' : '#1e1e1e'}
                                        borderRadius={8}
                                        padding="$3"
                                        justifyContent="space-between"
                                        alignItems="center"
                                        borderLeftColor={activePhase?.name === phase.name ? '#fed900' : 'transparent'}
                                        borderLeftWidth={2}
                                    >
                                        <XStack space="$2" alignItems="center">
                                            <Text fontSize={14}>{phase.icon}</Text>
                                            <YStack>
                                                <Text
                                                    color={activePhase?.name === phase.name ? '#fed900' : 'white'}
                                                    opacity={activePhase?.name === phase.name ? 1 : 0.7}
                                                    fontSize={13}
                                                >
                                                    {phase.name}
                                                </Text>
                                                <Text
                                                    color={activePhase?.name === phase.name ? '#fed900' : 'white'}
                                                    opacity={0.5}
                                                    fontSize={12}
                                                >
                                                    {formatTimeInterval(phase.startHour, phase.endHour)}
                                                </Text>
                                            </YStack>
                                        </XStack>
                                        <Text
                                            color={activePhase?.name === phase.name ? '#fed900' : 'white'}
                                            opacity={activePhase?.name === phase.name ? 1 : 0.5}
                                            fontSize={13}
                                            fontFamily="$mono"
                                        >
                                            {activePhase?.name === phase.name && 'ACTIVE'}
                                        </Text>
                                    </XStack>
                                ))}
                            </Stack>

                            {/* Level Progress Info & Actions */}
                            <YStack space="$3" borderTopWidth={1} borderTopColor="#2a2a2a" paddingTop="$3">
                                <Text fontSize={13} color="white">
                                    To reach the next level, you need to connect with <Text
                                    fontWeight="bold">{friendsNeeded} more
                                    friend{friendsNeeded !== 1 ? 's' : ''}.</Text>
                                </Text>
                                <XStack space="$2">
                                    <Link href="/(app)/(tabs)/Profile/UserFriends" asChild style={{flex: 1}}>
                                        <Button
                                            backgroundColor="#2a2a2a"
                                            color="white"
                                            icon={<EvilIcons name="search" size={24} color="white"/>}
                                        >
                                            Find Friends
                                        </Button>
                                    </Link>
                                    <Button
                                        backgroundColor="#2a2a2a"
                                        color="white"
                                        icon={<EvilIcons name="share-apple" size={24} color="white"/>}
                                        onPress={onShare}
                                        flex={1}
                                    >
                                        Share
                                    </Button>
                                </XStack>
                            </YStack>
                        </YStack>
                    )}
                </YStack>
            </Card>

            {/* Info Modal */}
            <Dialog modal open={infoOpen} onOpenChange={setInfoOpen}>
                <Adapt when="sm" platform="touch">
                    <Sheet animation="medium" zIndex={200000} modal dismissOnSnapToBottom>
                        <Sheet.Frame padding="$4" backgroundColor="#121212">
                            <Sheet.ScrollView>
                                <YStack space="$4">
                                    <XStack justifyContent="space-between" alignItems="center">
                                        <Text color="#fed900" fontSize={18} fontWeight="600">
                                            Phase Information
                                        </Text>
                                        <Button
                                            unstyled
                                            circular
                                            onPress={() => setInfoOpen(false)}
                                            icon={<Feather name="x" size={20} color="#fed900"/>}
                                        />
                                    </XStack>
                                    {phases.map((phase) => (
                                        <YStack key={phase.name} space="$2">
                                            <XStack space="$2" alignItems="center">
                                                <Text fontSize={16}>{phase.icon}</Text>
                                                <Text color="#fed900" fontSize={16} fontWeight="500">
                                                    {phase.name}
                                                </Text>
                                            </XStack>
                                            <Text color="white" opacity={0.8} fontSize={14}>
                                                {phase.description}
                                            </Text>
                                            <Text color="#fed900" fontSize={13} opacity={0.8}>
                                                {formatTimeInterval(phase.startHour, phase.endHour)}
                                            </Text>
                                        </YStack>
                                    ))}
                                </YStack>
                            </Sheet.ScrollView>
                        </Sheet.Frame>
                        <Sheet.Overlay
                            animation="lazy"
                            enterStyle={{opacity: 0}}
                            exitStyle={{opacity: 0}}
                        />
                    </Sheet>
                </Adapt>
            </Dialog>
        </>
    );
};

export default PhaseProgressCard;