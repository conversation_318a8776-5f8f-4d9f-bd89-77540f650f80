import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import {
    Image,
    Text,
    XStack,
    Y<PERSON>tack,
    Spinner,
} from 'tamagui';
import { Feather, FontAwesome6 } from '@expo/vector-icons';
import { Link } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import ConditionalMarqueeText from '@/components/Feed/FeedCard/ConditionalMarqueeText';
import { useMusicPlayerStore } from '@/stores/useMusicPlayerStore';
import { getUserData } from '@/api/usersAPI';
import CommentSection from './Feed/FeedCard/CommentSection';
import MusicProviderLink from './MusicProviderLink';

type Comment = {
    id: string;
    username: string;
    text: string;
    commenterProfilePicture?: string;
    createdAt: string;
    likes: number;
    isLiked: boolean;
    replies?: Comment[];
};

type PreviousPostItemProps = {
    postID: string;
    artworkURL: string;
    artistName: string;
    trackName: string;
    postRating: number;
    audioPreviewURL: string;
    spotifyURL?: string;
    trackURL?: string;
    comments: Comment[];
};

const PreviousPostItem: React.FC<PreviousPostItemProps> = ({
                                                               postID,
                                                               artworkURL,
                                                               artistName,
                                                               trackName,
                                                               postRating,
                                                               audioPreviewURL,
                                                               spotifyURL,
                                                               trackURL,
                                                               comments,
                                                           }) => {
    const [showComments, setShowComments] = useState(false);
    const { playingUrl, isPlaying, loadAndPlayTrack, togglePlayPause } =
        useMusicPlayerStore();

    const userData = useQuery({
        queryKey: ['userData'],
        queryFn: getUserData,
    });

    const isCurrentTrackPlaying = playingUrl === audioPreviewURL && isPlaying;

    const handleMusic = async () => {
        if (isCurrentTrackPlaying) {
            await togglePlayPause();
        } else {
            await loadAndPlayTrack(audioPreviewURL);
        }
    };

    return (
        <YStack
            width="100%"
            paddingVertical="$4"
            paddingHorizontal="$4"
            marginVertical="$3"
            borderRadius="$4"
            backgroundColor="#1A1A1A"
            // Subtle shadow for a “card” effect
            shadowColor="#000"
            shadowOffset={{ width: 0, height: 1 }}
            shadowOpacity={0.2}
            shadowRadius={4}
        >
            {/* Top Row: Artwork + Info + Provider Icons */}
            <XStack width="100%" alignItems="center" space="$3">
                {/* Album Artwork */}
                <Image
                    source={{ uri: artworkURL }}
                    style={{ width: 70, height: 70, borderRadius: 6 }}
                />

                {/* Track and Artist Info */}
                <YStack flex={1} space="$1">
                    <ConditionalMarqueeText
                        text={trackName}
                        fontSize={16}
                        bold
                        color="#FFFFFF"
                        width="100%"
                    />
                    <ConditionalMarqueeText
                        text={artistName}
                        fontSize={14}
                        color="#BBBBBB"
                        width="100%"
                    />
                    {/* Music Controls and Rating */}
                    <XStack alignItems="center" space="$2" marginTop="$2">
                        <TouchableOpacity onPress={handleMusic}>
                            <Feather
                                name={isCurrentTrackPlaying ? 'pause-circle' : 'play-circle'}
                                size={30}
                                color="#1DB954" // Spotify green for play/pause icon
                            />
                        </TouchableOpacity>
                        <Text fontSize={14} color="#FFFFFF">
                            {postRating} 🔥
                        </Text>
                    </XStack>
                </YStack>

                {/* Music Provider Icons */}
                {userData.isLoading ? (
                    <Spinner size="small" color="#FFFFFF" />
                ) : (
                    <>
                        <MusicProviderLink trackURL={trackURL} size={24} />
                    </>
                )}
            </XStack>

            {/* Comments Button */}
            <TouchableOpacity
                onPress={() => setShowComments(true)}
                style={{ marginTop: 12 }}
            >
                <XStack space="$2" alignItems="center">
                    <Feather name="message-circle" size={20} color="#FFFFFF" />
                    <Text color="#FFFFFF">{comments.length} Comments</Text>
                </XStack>
            </TouchableOpacity>

            {/* Comments Section */}
            <CommentSection
                postID={postID}
                comments={comments}
                hideActionButtons={true}
                showComments={showComments}
                setShowComments={setShowComments}
            />
        </YStack>
    );
};

export default PreviousPostItem;
