import {api} from './apiConfig'
import {MusicSearchResponse, Song} from '../types/MusicTypes'

export const searchAppleMusic = async ({queryKey}: {
    queryKey: [string, string, number?, number?]
}): Promise<Song[]> => {
    const [_key, searchTerm, offset = 0, limit = 20] = queryKey;
    try {
        const {data} = await api.get<MusicSearchResponse>(`/v1/search/music/apple`, {
            params: {
                query: searchTerm,
                offset,
                limit
            }
        });
        return data.results.songs.data;
    } catch (error) {
        throw error;
    }
};

export const searchUsers = async (query: string): Promise<any> => {
    try {

        const result = await api.get(`/v1/search/users/${query}`);
        return result.data;
    } catch (error) {
        if (error.response) {
            console.error('Error:', error.response.data.detail);
            throw new Error(error.response.data.detail);
        } else {
            console.error('Error:', error.message);
            throw new Error(error.message);
        }
    }
};

