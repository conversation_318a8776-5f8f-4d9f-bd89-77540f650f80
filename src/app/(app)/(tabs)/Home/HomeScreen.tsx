import React, {useState} from "react";
import {ActivityIndicator, RefreshControl, ScrollView} from "react-native";
import {SafeAreaView} from "react-native-safe-area-context";
import FeedCard from "@/components/Feed/FeedCard/FeedCard";
import {useQueryClient} from "@tanstack/react-query";
import {Button, Spacer, Text, XStack, YStack} from "tamagui";
import {useUserFeed} from "@/hooks/useUser";
import {invalidateQueries} from "@/utils/invalidateQueries";
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import {USER_FEED_QUERY_KEY, USER_LEVEL_QUERY_KEY} from "@/constants/queryKeys";


export default function HomeScreen() {
    const [refreshing, setRefreshing] = useState(false);
    const queryClient = useQueryClient();

    const {
        data: userFeedData,
        isLoading: isUserFeedLoading,
        isError: isUserFeedError,
        isSuccess: isUserFeedSuccess
    } = useUserFeed();

    //const {data: userData, isLoading: isUserDataLoading, isError: isUserDataError, isSuccess: isUserDataSuccess} = useUserData();


    const onRefresh = async () => {
        /**
         * This function will update all the necessary data when the user pulls down to refresh.
         */
        setRefreshing(true);
        await invalidateQueries({
            queryClient,
            queryKeys: [USER_FEED_QUERY_KEY, USER_LEVEL_QUERY_KEY],
        });
        setRefreshing(false);
    };

    const openRatingModal = () => {
        // Add your button functionality here
        console.log("Button pressed!");
    };



    if (isUserFeedLoading) {
        return (
            <YStack
                flex={1}
                justifyContent="center"
                alignItems="center"
            >
                <ActivityIndicator size="large" color="#fed900"/>
            </YStack>
        );
    }

    if (isUserFeedError) {
        return (
            <YStack
                flex={1}
                justifyContent="center"
                alignItems="center"
            >
                <Text>Error...</Text>
            </YStack>
        );
    }

    // TODO: Sort the post in the hook useUser?
    const sortedPosts = (userFeedData?.posts ?? []).sort((a, b) => new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime());


    return (
        <ScrollView
            style={{flex: 1}}
            showsVerticalScrollIndicator={false}
            refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh}/>
            }
        >
            <SafeAreaView>
                <XStack justifyContent="space-between" marginHorizontal={"$2"} paddingTop="$1">
                    <Text fontSize={30} fontWeight="bold" alignSelf="center">
                        Today's Bangers
                    </Text>
                    <Button
                        size="$5"
                        backgroundColor="transparent"
                        icon={<FontAwesome5 name="fire" size={50} color="white" />}
                        onPress={openRatingModal}
                    />
                </XStack>

                <YStack alignItems="center" gap="$2" paddingTop="$5">
                    {/*<PhaseProgressCard/>*/}
                    <Spacer size="$0.25"/>
                    {isUserFeedSuccess && sortedPosts.length === 0 ? (
                        <Text>No bangers yet from your friends</Text>
                    ) : (
                        sortedPosts.map((post, index) => (
                            <FeedCard
                                postID={post.postID}
                                key={index}
                                posterProfilePicture={post.posterProfilePicture}
                                posterUsername={post.posterUsername}
                                artworkURL={post.artworkURL}
                                artistName={post.artistName}
                                trackName={post.trackName}
                                postRating={post.postRating}
                                audioPreviewURL={post.audioPreviewURL}
                                spotifyURL={post.spotifyURL}
                                trackURL={post.trackURL}
                                userReaction={post.userReaction}
                                comments={post.comments}
                                audioCommentURL={post.audioCommentURL}
                                hideActionButtons={false}
                            />
                        ))
                    )}
                </YStack>
            </SafeAreaView>
            {/*<Leaderboard/>*/}
        </ScrollView>
    );
}